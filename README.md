# تطبيق الكاميرا الذكية للمراقبة - Smart Surveillance Camera App

## الوصف
تطبيق Android مطور بـ Kotlin يستخدم الذكاء الاصطناعي لكشف الأشخاص في الوقت الفعلي من خلال كاميرا الجوال. يعرض مربعات حمراء حول الأشخاص المكتشفين مع معلومات الثقة.

## الميزات الرئيسية
- 📱 **كاميرا في الوقت الفعلي**: عرض مباشر من كاميرا الجوال
- 🤖 **كشف ذكي للأشخاص**: استخدام ML Kit من Google لكشف الأشخاص
- 🔴 **مربعات تتبع حمراء**: رسم مربعات حمراء حول الأشخاص المكتشفين
- ⏱️ **فحص كل ثانيتين**: تحليل الصورة كل ثانيتين لتوفير الأداء
- 📊 **عداد الأشخاص**: عرض عدد الأشخاص المكتشفين في الوقت الفعلي
- 🔒 **إدارة الصلاحيات**: طلب صلاحيات الكاميرا بشكل آمن

## التقنيات المستخدمة
- **Kotlin** - لغة البرمجة الأساسية
- **Jetpack Compose** - واجهة المستخدم الحديثة
- **CameraX** - إدارة الكاميرا
- **ML Kit Object Detection** - كشف الأجسام والأشخاص
- **Coroutines** - البرمجة غير المتزامنة

## بنية المشروع
```
app/src/main/java/com/sccamera/
├── MainActivity.kt              # النشاط الرئيسي
├── CameraScreen.kt             # شاشة الكاميرا وإدارة الصلاحيات
├── ObjectDetectionAnalyzer.kt  # محلل كشف الأشخاص
├── BoundingBoxOverlay.kt       # رسم المربعات الحمراء
└── ui/theme/                   # ثيم التطبيق
```

## كيفية العمل
1. **طلب الصلاحيات**: التطبيق يطلب إذن الكاميرا عند التشغيل
2. **تشغيل الكاميرا**: عرض مباشر من الكاميرا الخلفية
3. **التحليل الذكي**: كل ثانيتين، يتم تحليل الصورة باستخدام ML Kit
4. **كشف الأشخاص**: البحث عن الأشخاص في الصورة
5. **رسم المربعات**: عرض مربعات حمراء حول الأشخاص المكتشفين
6. **عرض المعلومات**: إظهار نسبة الثقة وعدد الأشخاص

## متطلبات التشغيل
- Android API 24+ (Android 7.0)
- كاميرا خلفية
- ذاكرة RAM 3GB+ (مستحسن)
- مساحة تخزين 100MB

## التثبيت والتشغيل
1. افتح المشروع في Android Studio
2. تأكد من تحديث Gradle
3. قم بتشغيل التطبيق على جهاز حقيقي (مستحسن للأداء الأفضل)

## الاستخدام
1. افتح التطبيق
2. اسمح بصلاحية الكاميرا
3. وجه الكاميرا نحو المنطقة المراد مراقبتها
4. ستظهر مربعات حمراء حول أي شخص يدخل المجال
5. راقب العداد في أعلى الشاشة لمعرفة عدد الأشخاص

## ملاحظات مهمة
- التطبيق يعمل بشكل أفضل في الإضاءة الجيدة
- كلما كان الشخص أقرب للكاميرا، كانت دقة الكشف أعلى
- التحليل كل ثانيتين يوفر في استهلاك البطارية والمعالج
- يمكن كشف عدة أشخاص في نفس الوقت

## التطوير المستقبلي
- [ ] إضافة تسجيل الفيديو عند كشف حركة
- [ ] إشعارات عند كشف أشخاص
- [ ] حفظ لقطات الشاشة
- [ ] إعدادات حساسية الكشف
- [ ] دعم الكاميرا الأمامية
