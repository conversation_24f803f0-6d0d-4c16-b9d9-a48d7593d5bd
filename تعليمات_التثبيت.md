# 📱 تعليمات تثبيت واستخدام تطبيق الكاميرا الذكية

## 🎉 تم إنشاء ملفات APK محسّنة بنجاح!

### 📁 ملفات APK المتوفرة:

1. **نسخة التطوير المحسّنة (Debug)**
   - المسار: `app/build/outputs/apk/debug/app-debug.apk`
   - الحجم: محسّن للتثبيت
   - الاستخدام: جاهز للتثبيت المباشر
   - **التحسينات الجديدة**:
     - دعم أجهزة أندرويد أقدم (من Android 5.0)
     - إصلاح مشاكل التثبيت
     - دعم Multidex للتطبيقات الكبيرة
     - إعدادات أمان محسّنة

2. **نسخة الإنتاج (Release)**
   - المسار: `app/build/outputs/apk/release/app-release-unsigned.apk`
   - الحجم: أصغر ومحسّنة
   - الاستخدام: للاستخدام العادي (تحتاج توقيع)

## 🔧 خطوات التثبيت:

### الطريقة 1: تثبيت نسخة التطوير (الأسهل)
1. انسخ ملف `app-debug.apk` إلى هاتفك الأندرويد
2. افتح إعدادات الهاتف → الأمان → اسمح بالتثبيت من مصادر غير معروفة
3. اضغط على ملف APK لتثبيته
4. اتبع التعليمات على الشاشة

### الطريقة 2: التثبيت عبر ADB (للمطورين)
```bash
# تأكد من تفعيل USB Debugging
adb devices
adb install app/build/outputs/apk/debug/app-debug.apk
```

### الطريقة 3: التثبيت عبر Android Studio
1. افتح Android Studio
2. اذهب إلى Build → Build Bundle(s) / APK(s) → Build APK(s)
3. انتظر انتهاء البناء
4. اضغط على "locate" لفتح مجلد APK
5. انسخ الملف إلى الهاتف وثبته

### الطريقة 4: تثبيت نسخة الإنتاج (تحتاج توقيع)
نسخة الإنتاج تحتاج إلى توقيع رقمي. يمكنك:
- استخدام نسخة التطوير للاختبار
- أو توقيع النسخة باستخدام Android Studio

### الطريقة 5: التثبيت عبر مدير الملفات
1. انسخ ملف APK إلى مجلد Downloads في الهاتف
2. افتح مدير الملفات (File Manager)
3. اذهب إلى مجلد Downloads
4. اضغط على ملف app-debug.apk
5. اتبع التعليمات على الشاشة

## 📋 متطلبات النظام المحدّثة:
- **Android 5.0 (API 21) أو أحدث** (محسّن!)
- ذاكرة RAM: 2GB أو أكثر (مخفّض!)
- مساحة تخزين: 150MB
- كاميرا خلفية
- إذن الكاميرا

## 🚨 حل مشاكل التثبيت:

### إذا فشل التثبيت:

#### المشكلة 1: "التطبيق غير مثبت"
**الحلول:**
1. **تفعيل المصادر غير المعروفة:**
   - اذهب إلى: الإعدادات → الأمان → المصادر غير المعروفة
   - أو: الإعدادات → التطبيقات → الوصول الخاص → تثبيت تطبيقات غير معروفة
   - فعّل الخيار للمتصفح أو مدير الملفات

2. **تنظيف مساحة التخزين:**
   - احذف ملفات غير ضرورية
   - تأكد من وجود 200MB مساحة فارغة على الأقل

3. **إعادة تشغيل الهاتف:**
   - أعد تشغيل الجهاز وحاول مرة أخرى

#### المشكلة 2: "حزمة تالفة"
**الحلول:**
1. **إعادة تحميل APK:**
   - احذف الملف القديم
   - انسخ ملف APK جديد من الكمبيوتر

2. **استخدام مدير ملفات مختلف:**
   - جرب ES File Explorer أو File Manager+
   - تجنب المتصفحات لفتح APK

3. **التحقق من سلامة الملف:**
   - تأكد أن حجم الملف صحيح (حوالي 15-25 MB)

#### المشكلة 3: "فشل التحليل"
**الحلول:**
1. **تحديث نظام التشغيل:**
   - تأكد من تحديث Android إلى أحدث إصدار متاح

2. **إلغاء تثبيت النسخة القديمة:**
   - إذا كان التطبيق مثبت مسبقاً، احذفه أولاً

3. **استخدام ADB (للمطورين):**
   ```bash
   adb install app-debug.apk
   ```

#### المشكلة 4: "غير متوافق مع جهازك"
**الحلول:**
1. **تحقق من إصدار Android:**
   - يجب أن يكون Android 5.0 أو أحدث
   - اذهب إلى: الإعدادات → حول الهاتف → إصدار Android

2. **تحقق من معمارية المعالج:**
   - التطبيق يدعم ARM و ARM64
   - معظم الهواتف الحديثة مدعومة

#### المشكلة 5: "مساحة غير كافية"
**الحلول:**
1. **تنظيف التخزين:**
   - احذف صور وفيديوهات قديمة
   - امسح ذاكرة التخزين المؤقت للتطبيقات
   - انقل الملفات إلى بطاقة SD

2. **استخدام تطبيقات التنظيف:**
   - CCleaner أو Clean Master
   - تنظيف الملفات المؤقتة

## 🚀 كيفية الاستخدام:

### عند فتح التطبيق لأول مرة:
1. **طلب الصلاحيات**: سيطلب التطبيق إذن الكاميرا
2. **اضغط "منح الإذن"** للمتابعة
3. **تشغيل الكاميرا**: ستظهر شاشة الكاميرا مباشرة

### أثناء الاستخدام:
1. **وجه الكاميرا** نحو المنطقة المراد مراقبتها
2. **انتظر ثانيتين** - التطبيق يحلل الصورة كل ثانيتين
3. **مراقبة الكشف**: عندما يدخل شخص في المجال:
   - ستظهر **مربعات حمراء** حول الشخص
   - سيظهر **نص "Person"** مع نسبة الثقة
   - سيتحدث **العداد** في أعلى الشاشة

### مؤشرات التطبيق:
- **مربع أحمر**: شخص مكتشف
- **نص أبيض**: نوع الكائن ونسبة الثقة
- **عداد علوي**: عدد الأشخاص المكتشفين حالياً

## ⚡ نصائح للاستخدام الأمثل:

### للحصول على أفضل أداء:
- **إضاءة جيدة**: استخدم التطبيق في مكان مضاء جيداً
- **مسافة مناسبة**: الأشخاص على بعد 2-10 متر يُكتشفون بشكل أفضل
- **ثبات الكاميرا**: حاول تثبيت الهاتف لتجنب الاهتزاز
- **خلفية واضحة**: تجنب الخلفيات المعقدة

### توفير البطارية:
- التطبيق يحلل كل ثانيتين فقط لتوفير البطارية
- أغلق التطبيق عند عدم الحاجة
- استخدم وضع توفير الطاقة إذا لزم الأمر

## 🔧 استكشاف الأخطاء:

### إذا لم تظهر الكاميرا:
1. تأكد من منح إذن الكاميرا
2. أعد تشغيل التطبيق
3. تأكد من عدم استخدام تطبيق آخر للكاميرا

### إذا لم يكتشف الأشخاص:
1. تحسين الإضاءة
2. تقريب أو إبعاد الكاميرا
3. التأكد من وضوح الشخص في الكادر
4. انتظار ثانيتين للتحليل التالي

### إذا كان التطبيق بطيئاً:
1. أغلق التطبيقات الأخرى
2. أعد تشغيل الهاتف
3. تأكد من توفر ذاكرة كافية

## 📞 الدعم الفني:
- التطبيق مطور بـ Kotlin و ML Kit
- يستخدم الذكاء الاصطناعي من Google
- مفتوح المصدر ويمكن تطويره

## 🔄 التحديثات المستقبلية:
- إضافة تسجيل الفيديو
- إشعارات عند الكشف
- حفظ لقطات الشاشة
- إعدادات حساسية الكشف
- دعم الكاميرا الأمامية

---
**ملاحظة**: هذا التطبيق مخصص للاستخدام الشخصي والتعليمي. تأكد من احترام خصوصية الآخرين عند الاستخدام.
